import { useState, useCallback } from "react";
import { useProductCategories } from "@/data/useProductCategories";
import { useProducts } from "@/data/useProducts";
import { Card } from "primereact/card";
import { But<PERSON> } from "primereact/button";
import { Toolbar } from "primereact/toolbar";
import type {
  ColDef,
  ICellRendererParams,
  CellValueChangedEvent,
} from "ag-grid-community";
import { AgGridReact } from "ag-grid-react";
import type { Product, ProductCategory } from "@kassierer/shared/model";
import { PriceCentUtils } from "@/utils/priceCents";

import { ModuleRegistry, AllCommunityModule } from "ag-grid-community";

import 'ag-grid-community/styles/ag-grid.css';
import 'ag-grid-community/styles/ag-theme-quartz.css';

ModuleRegistry.registerModules([AllCommunityModule]);

// Temporary product type for local state
type TempProduct = {
  id: string;
  name: string;
  priceCents: number | null;
  categoryId: string | null;
  isTemporary: true;
};

// Combined type for table data
type TableProduct = (Product & { isTemporary?: false }) | TempProduct;

// Update type for temporary products
type TempProductUpdate = Partial<Omit<TempProduct, "id" | "isTemporary">>;

// Action cell renderer component
const ActionCellRenderer = ({
  data,
  onDelete,
  onDeleteTemp,
}: {
  data: TableProduct;
  onDelete: (product: Product) => void;
  onDeleteTemp: (id: string) => void;
}) => {
  const isTemporary = "isTemporary" in data && data.isTemporary;

  return (
    <div className="flex gap-2">
      <Button
        label="Löschen"
        icon="pi pi-trash"
        severity="danger"
        size="small"
        onClick={() => {
          if (isTemporary) {
            onDeleteTemp(data.id);
          } else {
            onDelete(data as Product);
          }
        }}
      />
    </div>
  );
};

function AdminProductsPage() {
  const products = useProducts((state) => state.products);
  const deleteProduct = useProducts((state) => state.deleteProduct);
  const createProduct = useProducts((state) => state.createProduct);
  const setProduct = useProducts((state) => state.setProduct);
  const categories = useProductCategories((state) => state.categories);

  // Local state for temporary products
  const [tempProducts, setTempProducts] = useState<TempProduct[]>([]);

  // Combine real and temporary products for display
  const allProducts: TableProduct[] = [...tempProducts, ...products];

  // Helper functions
  const addTempProduct = useCallback(() => {
    const newTempProduct: TempProduct = {
      id: crypto.randomUUID(),
      name: "",
      priceCents: null,
      categoryId: null,
      isTemporary: true,
    };
    setTempProducts((prev) => [newTempProduct, ...prev]);
  }, []);

  const updateTempProduct = useCallback(
    (id: string, updates: TempProductUpdate) => {
      setTempProducts((prev) =>
        prev.map((product) =>
          product.id === id ? { ...product, ...updates } : product,
        ),
      );
    },
    [],
  );

  const saveTempProduct = useCallback(
    (tempProduct: TempProduct) => {
      if (
        tempProduct.name &&
        tempProduct.priceCents !== null &&
        tempProduct.categoryId
      ) {
        createProduct({
          name: tempProduct.name,
          priceCents: tempProduct.priceCents,
          categoryId: tempProduct.categoryId,
        });
        setTempProducts((prev) => prev.filter((p) => p.id !== tempProduct.id));
      }
    },
    [createProduct],
  );

  const deleteTempProduct = useCallback((id: string) => {
    setTempProducts((prev) => prev.filter((p) => p.id !== id));
  }, []);

  // Handle cell value changes
  const onCellValueChanged = useCallback(
    (event: CellValueChangedEvent<TableProduct>) => {
      const { data, colDef, newValue } = event;
      const isTemporary = "isTemporary" in data && data.isTemporary;

      if (!isTemporary) {
        const product = products.find((p) => p.id === data.id);
        if (product) {
          setProduct(data.id, {
            ...product,
            [colDef.field!]: newValue,
          });
        }
        return;
      }

      if (isTemporary) {
        const field = colDef.field;
        const updates: TempProductUpdate = {};

        if (field === "name") {
          updates.name = newValue;
        } else if (field === "priceCents") {
          updates.priceCents = newValue;
        } else if (field === "categoryId") {
          updates.categoryId = newValue;
        }

        updateTempProduct(data.id, updates);

        // Check if product should be saved
        const updatedProduct = tempProducts.find((p) => p.id === data.id);
        if (updatedProduct) {
          const finalProduct = { ...updatedProduct, ...updates };
          if (
            finalProduct.name &&
            finalProduct.priceCents !== null &&
            finalProduct.categoryId
          ) {
            saveTempProduct(finalProduct);
          }
        }
      }
    },
    [updateTempProduct, tempProducts, saveTempProduct, products, setProduct],
  );

  const columnDefs: ColDef<TableProduct>[] = [
    {
      headerName: "Status",
      field: "isTemporary",
      width: 100,
      valueFormatter: (params) => {
        const isTemporary = params?.data?.isTemporary;
        return isTemporary ? "Neu" : "OK";
      },
      cellEditor: "agTextCellEditor",
      sortable: false,
      filter: false,
      editable: false,
    },
    {
      headerName: "Name",
      field: "name",
      flex: 1,
      editable: true,
      sortable: false,
      filter: true,
    },
    {
      headerName: "Preis",
      field: "priceCents",
      width: 150,
      valueFormatter: (params) => {
        return params.value !== null
          ? PriceCentUtils.toMoneyString(params.value)
          : "-";
      },
      editable: true,
      cellEditor: "agNumberCellEditor",
      cellEditorParams: {
        min: 0,
        precision: 0,
      },
      sortable: true,
      filter: "agNumberColumnFilter",
    },
    {
      headerName: "Kategorie",
      field: "categoryId",
      width: 200,
      editable: true,
      cellEditor: "agSelectCellEditor",
      cellEditorParams: {
        values: categories.map((cat) => cat.id),
      },
      valueFormatter: (params) => {
        const category = categories.find((cat) => cat.id === params.value);
        return category ? category.name : "";
      },
      sortable: true,
      filter: true,
    },
    {
      headerName: "Aktionen",
      width: 150,
      cellRenderer: (params: ICellRendererParams<TableProduct>) => (
        <ActionCellRenderer
          data={params.data!}
          onDelete={({ id }) => deleteProduct(id)}
          onDeleteTemp={deleteTempProduct}
        />
      ),
      sortable: false,
      filter: false,
      editable: false,
    },
  ];

  const startContent = (
    <>
      <Button
        icon="pi pi-plus"
        className="mr-2"
        onClick={addTempProduct}
        label="Neues Produkt"
      />
    </>
  );

  const endContent = <></>;

  return (
    <Card>
      <Toolbar start={startContent} end={endContent} className="mb-4" />
      <div className="ag-theme-quartz" style={{ minHeight: 400, width: "100%" }}>
        <AgGridReact
          rowData={allProducts}
          columnDefs={columnDefs}
          defaultColDef={{
            resizable: true,
            sortable: true,
            filter: true,
          }}
          pagination={true}
          paginationPageSize={10}
          domLayout="autoHeight"
          getRowId={(params) => params.data.id}
          onCellValueChanged={onCellValueChanged}
        />
      </div>
    </Card>
  );
}

export default AdminProductsPage;
